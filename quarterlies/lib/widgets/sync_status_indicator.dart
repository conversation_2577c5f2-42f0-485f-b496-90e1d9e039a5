import 'package:flutter/material.dart';
import 'package:quarterlies/models/sync_status.dart';
import 'package:quarterlies/services/sync_service.dart' hide SyncStatus;

/// A widget that displays the sync status of an item
///
/// This widget shows a small icon that indicates whether an item is synced,
/// pending sync, or has a sync error.
class SyncStatusIndicator extends StatelessWidget {
  final SyncStatus status;
  final double size;
  final VoidCallback? onTap;
  final bool showLabel;
  final bool showDetails;
  final String? detailMessage;

  const SyncStatusIndicator({
    super.key,
    required this.status,
    this.size = 16.0,
    this.onTap,
    this.showLabel = false,
    this.showDetails = false,
    this.detailMessage,
  });

  @override
  Widget build(BuildContext context) {
    late IconData icon;
    late Color color;
    late String tooltip;

    switch (status) {
      case SyncStatus.synced:
        icon = Icons.cloud_done;
        color = Colors.green;
        tooltip = 'Synced with server';
        break;
      case SyncStatus.pending:
        icon = Icons.cloud_upload;
        color = Colors.orange;
        tooltip = 'Pending sync';
        break;
      case SyncStatus.error:
        icon = Icons.cloud_off;
        color = Colors.red;
        tooltip = 'Sync error';
        break;
      case SyncStatus.conflict:
        icon = Icons.sync_problem;
        color = Colors.deepOrange;
        tooltip = 'Sync conflict - needs resolution';
        break;
    }

    Widget statusIcon = Icon(icon, color: color, size: size);

    // If this is a conflict and has an onTap handler, make it tappable
    if (status == SyncStatus.conflict && onTap != null) {
      statusIcon = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(size),
        child: statusIcon,
      );
    }

    // If showLabel is true, display the status text next to the icon
    if (showLabel) {
      Widget statusWidget = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          statusIcon,
          const SizedBox(width: 4),
          Text(
            status == SyncStatus.conflict
                ? 'Resolve'
                : status.name.toUpperCase(),
            style: TextStyle(
              fontSize: size * 0.75,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      );

      // If showDetails is true and we have a conflict or error, show additional details
      if (showDetails &&
          (status == SyncStatus.conflict || status == SyncStatus.error)) {
        return Tooltip(
          message: tooltip,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              statusWidget,
              if (detailMessage != null) ...[
                const SizedBox(height: 4),
                Text(
                  detailMessage!,
                  style: TextStyle(
                    fontSize: size * 0.7,
                    color: Colors.grey[700],
                  ),
                ),
              ],
              const SizedBox(height: 4),
              // Add a manual sync button for conflicts and errors
              InkWell(
                onTap:
                    onTap ??
                    () {
                      // If no specific onTap handler is provided, use the default sync service
                      SyncService().forceSyncData();
                    },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 25 / 255),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: color.withValues(alpha: 128 / 255),
                    ),
                  ),
                  child: Text(
                    status == SyncStatus.conflict
                        ? 'Resolve Conflict'
                        : 'Retry Sync',
                    style: TextStyle(
                      fontSize: size * 0.7,
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }

      return Tooltip(message: tooltip, child: statusWidget);
    }

    // If we're showing a conflict or error, make the icon more noticeable with animation
    if (status == SyncStatus.conflict || status == SyncStatus.error) {
      return Tooltip(
        message: tooltip,
        child: TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0.8, end: 1.2),
          duration: const Duration(seconds: 1),
          builder: (context, value, child) {
            return Transform.scale(scale: value, child: statusIcon);
          },
          onEnd: () {
            // Restart the animation by rebuilding the widget if still mounted
            if (context.mounted) {
              (context as Element).markNeedsBuild();
            }
          },
        ),
      );
    }

    return Tooltip(message: tooltip, child: statusIcon);
  }
}
