import 'package:flutter/material.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/screens/time_logs/time_log_detail_screen.dart';
import 'package:quarterlies/screens/time_logs/time_log_form_screen.dart';
import 'package:quarterlies/widgets/sync_status_indicator.dart';
import 'package:quarterlies/widgets/adaptive_list_tile.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';

class TimeLogListScreen extends StatefulWidget {
  final String? jobId; // Optional job ID to filter time logs

  const TimeLogListScreen({super.key, this.jobId});

  @override
  State<TimeLogListScreen> createState() => _TimeLogListScreenState();
}

class _TimeLogListScreenState extends State<TimeLogListScreen> {
  final DataRepository _dataRepository = DataRepository();
  List<TimeLog> _timeLogs = [];
  List<TimeLog> _filteredTimeLogs = [];
  List<Job> _jobs = [];
  String? _selectedJobId;
  bool _isOffline = false;
  String? _errorMessage;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    _selectedJobId = widget.jobId;
    _loadData();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show sync feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadTimeLogData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Load jobs for filtering
          final jobs = await _dataRepository.getJobs();

          // Load time logs based on job ID filter
          final timeLogs =
              _selectedJobId != null
                  ? await _dataRepository.getTimeLogsByJob(_selectedJobId!)
                  : await _dataRepository.getTimeLogs();

          if (!mounted) return;

          setState(() {
            _jobs = jobs;
            _timeLogs = timeLogs;
            _applyFilters();
          });
        },
        message: 'Loading time logs...',
        errorMessage: 'Failed to load time logs',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load time logs: ${e.toString()}';
        });
      }
    }
  }

  // Sync data with server
  Future<void> _syncData() async {
    if (_isOffline) {
      ErrorDisplay.showWarning(
        context,
        'Cannot sync while offline. Please check your connection.',
      );
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithSyncLoading(() async {
        // Attempt to sync all pending time logs
        await _dataRepository.syncTimeLogs();

        // Reload data after sync
        await _loadData();
      }, operationName: 'Syncing time logs...');

      // Check if widget is still mounted before using context
      if (mounted) {
        ErrorDisplay.showOperation(context, 'Time logs synced successfully');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to sync time logs: ${e.toString()}';
        });

        ErrorDisplay.showWarning(context, 'Sync failed: ${e.toString()}');
      }
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredTimeLogs =
          _timeLogs.where((timeLog) {
            // Apply job filter if selected
            if (_selectedJobId != null && timeLog.jobId != _selectedJobId) {
              return false;
            }

            return true;
          }).toList();

      // Sort by date, most recent first
      _filteredTimeLogs.sort((a, b) => b.date.compareTo(a.date));
    });
  }

  String _getJobTitle(String jobId) {
    final job = _jobs.firstWhere(
      (job) => job.id == jobId,
      orElse:
          () => Job(id: '', userId: '', customerId: '', title: 'Unknown Job'),
    );
    return job.title;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.jobId != null ? 'Time Logs for Job' : 'All Time Logs',
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // Offline status indicator
          if (_isOffline)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Tooltip(
                message: 'You are offline. Using locally stored data.',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: const [
                    Icon(Icons.cloud_off, color: Colors.white),
                    SizedBox(width: 4),
                    Text('Offline', style: TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            ),
          if (_jobs.isNotEmpty && widget.jobId == null)
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: () => _showFilterDialog(),
            ),
          // Manual sync button
          Consumer<LoadingStateProvider>(
            builder: (context, loadingProvider, child) {
              return IconButton(
                icon:
                    loadingProvider.isSyncing
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: SyncLoadingIndicator(
                            operation: 'Syncing...',
                            size: 16.0,
                            showSyncIcon: false,
                          ),
                        )
                        : const Icon(Icons.sync),
                tooltip:
                    loadingProvider.isSyncing
                        ? loadingProvider.syncOperation ?? 'Syncing...'
                        : 'Sync data with server',
                onPressed: loadingProvider.isSyncing ? null : _syncData,
              );
            },
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToFormScreen(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildBody() {
    return Consumer<LoadingStateProvider>(
      builder: (context, loadingProvider, child) {
        final isLoading = loadingProvider.isLoading('loadTimeLogData');

        if (isLoading) {
          return const Center(
            child: QuarterliesLoadingIndicator(
              message: 'Loading time logs...',
              size: 32.0,
              showOfflineStatus: true,
            ),
          );
        }

        if (_errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(_errorMessage!, style: const TextStyle(color: Colors.red)),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadData,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (_filteredTimeLogs.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('No time logs found'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => _navigateToFormScreen(),
                  child: const Text('Add Time Log'),
                ),
              ],
            ),
          );
        }

        // Use RefreshIndicator for pull-to-refresh functionality
        return RefreshIndicator(
          onRefresh: () async {
            await _syncData();
          },
          child: ListView.builder(
            itemCount: _filteredTimeLogs.length,
            itemBuilder: (context, index) {
              final timeLog = _filteredTimeLogs[index];
              return Consumer<DisplaySettingsProvider>(
                builder: (context, displayProvider, child) {
                  return AdaptiveListTile(
                    title: Row(
                      children: [
                        Expanded(
                          child: Text(
                            '${DateFormat('MMM d, yyyy').format(timeLog.date)} - ${timeLog.hours} hours',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        // Enhanced sync status indicator with conflict resolution
                        SyncStatusIndicator(
                          status: timeLog.syncStatus,
                          onTap:
                              timeLog.syncStatus == SyncStatus.conflict
                                  ? () => _showConflictResolutionDialog(timeLog)
                                  : null,
                        ),
                      ],
                    ),
                    subtitle: Text(
                      'Job: ${_getJobTitle(timeLog.jobId)} • \$${timeLog.laborCost.toStringAsFixed(2)}${timeLog.notes != null && timeLog.notes!.isNotEmpty ? ' • ${timeLog.notes}' : ''}',
                    ),
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.timer,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    trailing: Text(
                      timeLog.isFlatRate
                          ? 'Flat Rate'
                          : '\$${timeLog.hourlyRate.toStringAsFixed(2)}/hr',
                      style: TextStyle(
                        color: Colors.blue[800],
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                    // Additional info shown only in Office Mode
                    additionalInfo:
                        displayProvider.isOfficeMode
                            ? OfficeAdditionalInfo(
                              items: [
                                InfoItem(
                                  label: 'Rate',
                                  value:
                                      '\$${timeLog.hourlyRate.toStringAsFixed(2)}/hr',
                                  icon: Icons.attach_money,
                                ),
                                if (timeLog.isFlatRate)
                                  const InfoItem(
                                    label: 'Type',
                                    value: 'Flat Rate',
                                    icon: Icons.payment,
                                  ),
                                if (timeLog.voiceNoteUrl != null)
                                  const InfoItem(
                                    label: 'Voice Note',
                                    value: 'Available',
                                    icon: Icons.mic,
                                  ),
                                if (timeLog.syncStatus != SyncStatus.synced)
                                  InfoItem(
                                    label: 'Sync',
                                    value:
                                        timeLog.syncStatus
                                            .toString()
                                            .split('.')
                                            .last,
                                    icon: Icons.sync_problem,
                                  ),
                              ],
                            )
                            : null,
                    // Office actions shown only in Office Mode
                    officeActions:
                        displayProvider.isOfficeMode
                            ? [
                              OfficeActionButton(
                                icon: Icons.edit,
                                label: 'Edit',
                                onPressed: () async {
                                  final result = await Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) => TimeLogFormScreen(
                                            timeLog: timeLog,
                                          ),
                                    ),
                                  );
                                  if (result == true) {
                                    _loadData();
                                  }
                                },
                              ),
                              if (timeLog.voiceNoteUrl != null)
                                OfficeActionButton(
                                  icon: Icons.play_arrow,
                                  label: 'Play',
                                  onPressed: () {
                                    ErrorDisplay.showInfo(
                                      context,
                                      'Voice note player coming soon!',
                                    );
                                  },
                                ),
                              OfficeActionButton(
                                icon: Icons.copy,
                                label: 'Duplicate',
                                onPressed: () {
                                  ErrorDisplay.showInfo(
                                    context,
                                    'Duplicate functionality coming soon!',
                                  );
                                },
                              ),
                            ]
                            : null,
                    onTap: () => _navigateToDetailScreen(timeLog),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Filter Time Logs'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DropdownButtonFormField<String?>(
                  decoration: const InputDecoration(labelText: 'Filter by Job'),
                  value: _selectedJobId,
                  items: [
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('All Jobs'),
                    ),
                    ..._jobs.map(
                      (job) => DropdownMenuItem<String?>(
                        value: job.id,
                        child: Text(job.title),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedJobId = value;
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _applyFilters();
              },
              child: const Text('Apply'),
            ),
          ],
        );
      },
    );
  }

  void _navigateToDetailScreen(TimeLog timeLog) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TimeLogDetailScreen(timeLog: timeLog),
      ),
    ).then((_) => _loadData());
  }

  void _navigateToFormScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TimeLogFormScreen(jobId: _selectedJobId),
      ),
    ).then((_) => _loadData());
  }

  // Show conflict resolution dialog
  void _showConflictResolutionDialog(TimeLog timeLog) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Resolve Sync Conflict'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'This time log has conflicting versions between your device and the server.',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  const Text('Choose which version to keep:'),
                  const SizedBox(height: 8),
                  Card(
                    color: Colors.blue.shade50,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Local Version (This Device)',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            'Date: ${DateFormat('MMM d, yyyy').format(timeLog.date)}',
                          ),
                          Text('Hours: ${timeLog.hours}'),
                          Text(
                            'Rate: \$${timeLog.hourlyRate.toStringAsFixed(2)}',
                          ),
                          if (timeLog.notes != null &&
                              timeLog.notes!.isNotEmpty)
                            Text('Notes: ${timeLog.notes}'),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Note: In a real implementation, you would fetch the server version
                  // and display it here for comparison
                  Card(
                    color: Colors.green.shade50,
                    child: const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Server Version',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text('Loading server version...'),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                onPressed: () {
                  // Keep local version
                  _resolveConflict(timeLog, keepLocal: true);
                  Navigator.pop(context);
                },
                child: const Text('Keep Local Version'),
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                onPressed: () {
                  // Keep server version
                  _resolveConflict(timeLog, keepLocal: false);
                  Navigator.pop(context);
                },
                child: const Text('Keep Server Version'),
              ),
            ],
          ),
    );
  }

  // Resolve a sync conflict
  Future<void> _resolveConflict(
    TimeLog timeLog, {
    required bool keepLocal,
  }) async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'resolveConflict',
        () async {
          // In a real implementation, you would call a method in your DataRepository
          // to resolve the conflict based on the user's choice
          await _dataRepository.resolveTimeLogConflict(
            timeLog.id,
            keepLocal: keepLocal,
          );

          // Reload data after resolving conflict
          await _loadData();
        },
        message: 'Resolving conflict...',
        errorMessage: 'Failed to resolve conflict',
      );

      // Check if widget is still mounted before using context
      if (mounted) {
        ErrorDisplay.showOperation(context, 'Conflict resolved successfully');
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Failed to resolve conflict: ${e.toString()}',
        );
      }
    }
  }
}
