import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/widgets/custom_widgets.dart';
import 'package:quarterlies/widgets/address_autocomplete_field.dart';
import 'package:quarterlies/widgets/adaptive_form_section.dart';

class BusinessInfoStep extends StatefulWidget {
  final UserSettings userSettings;
  final Function(UserSettings) onSettingsUpdated;
  final VoidCallback onNext;
  final VoidCallback onPrevious;
  final bool isLoading;

  const BusinessInfoStep({
    super.key,
    required this.userSettings,
    required this.onSettingsUpdated,
    required this.onNext,
    required this.onPrevious,
    required this.isLoading,
  });

  @override
  State<BusinessInfoStep> createState() => _BusinessInfoStepState();
}

class _BusinessInfoStepState extends State<BusinessInfoStep> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _businessNameController;
  late TextEditingController _businessAddressController;
  late TextEditingController _businessPhoneController;
  late TextEditingController _businessEmailController;
  late TextEditingController _defaultInvoiceNotesController;
  late TextEditingController _defaultInvoiceTermsController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _businessNameController = TextEditingController(
      text: widget.userSettings.businessName,
    );
    _businessAddressController = TextEditingController(
      text: widget.userSettings.businessAddress,
    );
    _businessPhoneController = TextEditingController(
      text: widget.userSettings.businessPhone,
    );
    _businessEmailController = TextEditingController(
      text: widget.userSettings.businessEmail,
    );
    _defaultInvoiceNotesController = TextEditingController(
      text: widget.userSettings.defaultInvoiceNotes,
    );
    _defaultInvoiceTermsController = TextEditingController(
      text: widget.userSettings.defaultInvoiceTerms,
    );
  }

  @override
  void dispose() {
    _businessNameController.dispose();
    _businessAddressController.dispose();
    _businessPhoneController.dispose();
    _businessEmailController.dispose();
    _defaultInvoiceNotesController.dispose();
    _defaultInvoiceTermsController.dispose();
    super.dispose();
  }

  void _updateSettings() {
    if (_formKey.currentState!.validate()) {
      final updatedSettings = widget.userSettings.copyWith(
        businessName:
            _businessNameController.text.trim().isEmpty
                ? null
                : _businessNameController.text.trim(),
        businessAddress:
            _businessAddressController.text.trim().isEmpty
                ? null
                : _businessAddressController.text.trim(),
        businessPhone:
            _businessPhoneController.text.trim().isEmpty
                ? null
                : _businessPhoneController.text.trim(),
        businessEmail:
            _businessEmailController.text.trim().isEmpty
                ? null
                : _businessEmailController.text.trim(),
        defaultInvoiceNotes:
            _defaultInvoiceNotesController.text.trim().isEmpty
                ? null
                : _defaultInvoiceNotesController.text.trim(),
        defaultInvoiceTerms:
            _defaultInvoiceTermsController.text.trim().isEmpty
                ? null
                : _defaultInvoiceTermsController.text.trim(),
      );

      widget.onSettingsUpdated(updatedSettings);
      widget.onNext();
    }
  }

  void _skipBusinessInfo() {
    // Allow users to skip business info and continue with defaults
    widget.onNext();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Padding(
          padding: EdgeInsets.all(displayProvider.isOfficeMode ? 16.0 : 24.0),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Text(
                          'Business Information',
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 20 : 24,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        SizedBox(
                          height: displayProvider.isOfficeMode ? 8.0 : 12.0,
                        ),
                        Text(
                          'Set up your business details for invoices, estimates, and contracts. You can skip this step and add details later.',
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 14 : 16,
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 0.7),
                            height: 1.4,
                          ),
                        ),
                        SizedBox(
                          height: displayProvider.isOfficeMode ? 20.0 : 32.0,
                        ),

                        // Business details section
                        AdaptiveFormSection(
                          title: 'Business Details',
                          icon: Icons.business,
                          useGridLayout: displayProvider.isOfficeMode,
                          children: [
                            AdaptiveFormField(
                              label: 'Business Name',
                              child: CustomTextField(
                                controller: _businessNameController,
                                labelText: 'Business Name',
                                hintText: 'Enter your business name',
                              ),
                            ),
                            AdaptiveFormField(
                              label: 'Business Address',
                              child: AddressAutocompleteField(
                                controller: _businessAddressController,
                                labelText: 'Business Address',
                                hintText: 'Enter your business address',
                                onAddressSelected: (address) {
                                  // Use the full formatted address
                                  _businessAddressController.text =
                                      address.formattedAddress;
                                },
                              ),
                            ),
                          ],
                        ),

                        // Contact information section
                        AdaptiveFormSection(
                          title: 'Business Contact',
                          icon: Icons.contact_phone,
                          useGridLayout: displayProvider.isOfficeMode,
                          children: [
                            AdaptiveFormField(
                              label: 'Business Phone',
                              child: CustomTextField(
                                controller: _businessPhoneController,
                                labelText: 'Business Phone',
                                hintText: 'Enter your business phone number',
                                keyboardType: TextInputType.phone,
                              ),
                            ),
                            AdaptiveFormField(
                              label: 'Business Email',
                              child: CustomTextField(
                                controller: _businessEmailController,
                                labelText: 'Business Email',
                                hintText: 'Enter your business email address',
                                keyboardType: TextInputType.emailAddress,
                                validator: (value) {
                                  if (value != null &&
                                      value.trim().isNotEmpty) {
                                    if (!RegExp(
                                      r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                    ).hasMatch(value)) {
                                      return 'Please enter a valid email address';
                                    }
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ],
                        ),

                        // Invoice defaults section
                        AdaptiveFormSection(
                          title: 'Invoice Defaults (Optional)',
                          icon: Icons.receipt_long,
                          children: [
                            AdaptiveFormField(
                              label: 'Default Invoice Notes',
                              helpText:
                                  'These notes will appear on all new invoices',
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12.0,
                                ),
                                child: TextFormField(
                                  controller: _defaultInvoiceNotesController,
                                  maxLines: 3,
                                  style: const TextStyle(fontSize: 18),
                                  decoration: InputDecoration(
                                    labelText: 'Default Invoice Notes',
                                    hintText:
                                        'e.g., Thank you for your business!',
                                    labelStyle: TextStyle(
                                      fontSize: 16,
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    filled: true,
                                    fillColor:
                                        Theme.of(context).colorScheme.surface,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 18,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            AdaptiveFormField(
                              label: 'Default Invoice Terms',
                              helpText:
                                  'Payment terms and conditions for invoices',
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12.0,
                                ),
                                child: TextFormField(
                                  controller: _defaultInvoiceTermsController,
                                  maxLines: 3,
                                  style: const TextStyle(fontSize: 18),
                                  decoration: InputDecoration(
                                    labelText: 'Default Invoice Terms',
                                    hintText:
                                        'e.g., Payment due within 30 days',
                                    labelStyle: TextStyle(
                                      fontSize: 16,
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    filled: true,
                                    fillColor:
                                        Theme.of(context).colorScheme.surface,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 18,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),

                        // Info box
                        Container(
                          margin: EdgeInsets.only(
                            top: displayProvider.isOfficeMode ? 16.0 : 24.0,
                          ),
                          padding: EdgeInsets.all(
                            displayProvider.isOfficeMode ? 12.0 : 16.0,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context)
                                .colorScheme
                                .primaryContainer
                                .withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Theme.of(
                                context,
                              ).colorScheme.primary.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: Theme.of(context).colorScheme.primary,
                                size: displayProvider.isOfficeMode ? 20 : 24,
                              ),
                              SizedBox(
                                width:
                                    displayProvider.isOfficeMode ? 8.0 : 12.0,
                              ),
                              Expanded(
                                child: Text(
                                  'Don\'t worry if you don\'t have all this information now. You can always update these details later in Settings.',
                                  style: TextStyle(
                                    fontSize:
                                        displayProvider.isOfficeMode ? 13 : 14,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withValues(alpha: 0.8),
                                    height: 1.3,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Navigation buttons
                SizedBox(height: displayProvider.isOfficeMode ? 16.0 : 24.0),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: widget.isLoading ? null : widget.onPrevious,
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            vertical: displayProvider.isOfficeMode ? 12 : 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'Back',
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 16 : 18,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: displayProvider.isOfficeMode ? 8.0 : 12.0),
                    Expanded(
                      child: OutlinedButton(
                        onPressed: widget.isLoading ? null : _skipBusinessInfo,
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            vertical: displayProvider.isOfficeMode ? 12 : 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'Skip',
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 16 : 18,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: displayProvider.isOfficeMode ? 8.0 : 12.0),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: widget.isLoading ? null : _updateSettings,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(
                            vertical: displayProvider.isOfficeMode ? 12 : 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child:
                            widget.isLoading
                                ? SizedBox(
                                  height:
                                      displayProvider.isOfficeMode ? 20 : 24,
                                  width: displayProvider.isOfficeMode ? 20 : 24,
                                  child: const CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                )
                                : Text(
                                  'Continue',
                                  style: TextStyle(
                                    fontSize:
                                        displayProvider.isOfficeMode ? 16 : 18,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
