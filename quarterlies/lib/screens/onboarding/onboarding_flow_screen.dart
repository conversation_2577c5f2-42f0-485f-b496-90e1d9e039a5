import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/auth_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/screens/onboarding/personal_info_step.dart';
import 'package:quarterlies/screens/onboarding/business_info_step.dart';
import 'package:quarterlies/screens/onboarding/signature_step.dart';
import 'package:quarterlies/screens/onboarding/settings_review_step.dart';
import 'package:quarterlies/screens/onboarding/welcome_step.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

class OnboardingFlowScreen extends StatefulWidget {
  const OnboardingFlowScreen({super.key});

  @override
  State<OnboardingFlowScreen> createState() => _OnboardingFlowScreenState();
}

class _OnboardingFlowScreenState extends State<OnboardingFlowScreen> {
  final PageController _pageController = PageController();
  final DataRepository _dataRepository = DataRepository();
  final AuthService _authService = AuthService();

  int _currentStep = 0;
  String? _errorMessage;

  // Data collected during onboarding
  UserProfile? _userProfile;
  UserSettings? _userSettings;

  final List<String> _stepTitles = [
    'Welcome',
    'Personal Information',
    'Business Information',
    'Create Signature',
    'Settings Review',
  ];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'initializeOnboarding',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final userId = _authService.currentUser?.id;
          if (userId == null) {
            throw Exception('User not authenticated');
          }

          // Initialize with default user profile
          _userProfile = UserProfile(
            userId: userId,
            email: _authService.currentUser?.email,
          );

          // Initialize with default user settings
          _userSettings = UserSettings(userId: userId);
        },
        message: 'Setting up your account...',
        errorMessage: 'Failed to initialize onboarding',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    }
  }

  void _nextStep() {
    if (_currentStep < _stepTitles.length - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _completeOnboarding() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'completeOnboarding',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Mark onboarding as complete
          final completedProfile = _userProfile!.copyWith(
            isOnboardingComplete: true,
          );

          // Save user profile and settings
          await _dataRepository.createUserProfile(completedProfile);
          await _dataRepository.updateUserSettings(_userSettings!);

          if (mounted) {
            // Show completion feedback
            ErrorDisplay.showSuccess(
              context,
              'Account setup completed successfully! Welcome to Quarterlies.',
            );

            // Navigate to main app
            Navigator.pushReplacementNamed(context, '/home');
          }
        },
        message: 'Completing setup...',
        errorMessage: 'Failed to complete onboarding',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    }
  }

  void _updateUserProfile(UserProfile profile) {
    setState(() {
      _userProfile = profile;
    });
  }

  void _updateUserSettings(UserSettings settings) {
    setState(() {
      _userSettings = settings;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<DisplaySettingsProvider, LoadingStateProvider>(
      builder: (context, displayProvider, loadingProvider, child) {
        if (loadingProvider.isLoading('initializeOnboarding') &&
            _userProfile == null) {
          return const Scaffold(
            body: Center(
              child: QuarterliesLoadingIndicator(
                message: 'Setting up your account...',
              ),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(
              'Setup Your Account',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 18 : 22,
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            elevation: 4,
            toolbarHeight: displayProvider.isOfficeMode ? 56 : 64,
            automaticallyImplyLeading:
                false, // Prevent back navigation during onboarding
          ),
          body: Column(
            children: [
              // Progress indicator
              Container(
                padding: EdgeInsets.all(
                  displayProvider.isOfficeMode ? 12.0 : 16.0,
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Step ${_currentStep + 1} of ${_stepTitles.length}',
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 14 : 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          _stepTitles[_currentStep],
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 14 : 16,
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: displayProvider.isOfficeMode ? 8.0 : 12.0),
                    QuarterliesProgressIndicator(
                      value: (_currentStep + 1) / _stepTitles.length,
                      label: 'Setup Progress',
                      showPercentage: false,
                    ),
                  ],
                ),
              ),

              // Error message
              if (_errorMessage != null)
                Container(
                  margin: EdgeInsets.all(
                    displayProvider.isOfficeMode ? 12.0 : 16.0,
                  ),
                  padding: EdgeInsets.all(
                    displayProvider.isOfficeMode ? 12.0 : 16.0,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    border: Border.all(color: Colors.red[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error, color: Colors.red[700]),
                      SizedBox(
                        width: displayProvider.isOfficeMode ? 8.0 : 12.0,
                      ),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(
                            color: Colors.red[700],
                            fontSize: displayProvider.isOfficeMode ? 14 : 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // Page content
              Expanded(
                child: PageView(
                  controller: _pageController,
                  physics:
                      const NeverScrollableScrollPhysics(), // Disable swipe navigation
                  children: [
                    WelcomeStep(
                      onNext: _nextStep,
                      isLoading: loadingProvider.isLoading(
                        'initializeOnboarding',
                      ),
                    ),
                    PersonalInfoStep(
                      userProfile: _userProfile!,
                      onProfileUpdated: _updateUserProfile,
                      onNext: _nextStep,
                      onPrevious: _previousStep,
                      isLoading: false, // No async operations in this step
                    ),
                    BusinessInfoStep(
                      userSettings: _userSettings!,
                      onSettingsUpdated: _updateUserSettings,
                      onNext: _nextStep,
                      onPrevious: _previousStep,
                      isLoading: false, // No async operations in this step
                    ),
                    SignatureStep(
                      userProfile: _userProfile!,
                      onProfileUpdated: _updateUserProfile,
                      onNext: _nextStep,
                      onPrevious: _previousStep,
                      isLoading:
                          false, // Signature creation is handled internally
                    ),
                    SettingsReviewStep(
                      userProfile: _userProfile!,
                      userSettings: _userSettings!,
                      onProfileUpdated: _updateUserProfile,
                      onSettingsUpdated: _updateUserSettings,
                      onComplete: _completeOnboarding,
                      onPrevious: _previousStep,
                      isLoading: loadingProvider.isLoading(
                        'completeOnboarding',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
