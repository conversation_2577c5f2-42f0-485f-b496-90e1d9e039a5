import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/screens/login_screen.dart';
import 'package:quarterlies/screens/signup_screen.dart';
import 'package:quarterlies/screens/password_reset_screen.dart';
import 'package:quarterlies/screens/update_password_screen.dart';
import 'package:quarterlies/screens/onboarding/onboarding_flow_screen.dart';
import 'package:quarterlies/screens/auth_wrapper_screen.dart';
import 'package:quarterlies/screens/customers/index.dart';
import 'package:quarterlies/screens/jobs/index.dart';
import 'package:quarterlies/screens/mileage/index.dart';
import 'package:quarterlies/screens/main_navigation_screen.dart';
import 'package:quarterlies/services/security_provider.dart';
import 'package:quarterlies/services/notification_service.dart';
import 'package:quarterlies/services/invoice_notification_checker.dart';
import 'package:quarterlies/services/mileage_tracking_service.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/sync_manager.dart';
import 'package:quarterlies/services/voice_recording_service.dart';
import 'package:quarterlies/services/invoice_pdf_service.dart';
import 'package:quarterlies/services/contract_pdf_service.dart';
import 'package:quarterlies/services/estimate_pdf_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/providers/customer_provider.dart';
import 'package:quarterlies/providers/job_provider.dart';
import 'package:quarterlies/providers/financial_provider.dart';
import 'package:quarterlies/providers/tax_payment_provider.dart';
import 'package:quarterlies/providers/invoice_provider.dart';
import 'package:quarterlies/providers/expense_provider.dart';
import 'package:quarterlies/utils/field_friendly_theme.dart';
import 'package:quarterlies/utils/error_handler.dart';

// Global navigator key to enable navigation from anywhere
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set up global error handling
  FlutterError.onError = (FlutterErrorDetails details) {
    final appError = AppError.fromException(
      details.exception,
      stackTrace: details.stack,
      context: {
        'library': details.library,
        'context': details.context?.toString(),
      },
    );
    ErrorHandler.logError(appError);

    // In debug mode, also show the default Flutter error handling
    if (kDebugMode) {
      FlutterError.presentError(details);
    }
  };

  // Handle errors outside of Flutter framework
  PlatformDispatcher.instance.onError = (error, stack) {
    final appError = AppError.fromException(
      error,
      stackTrace: stack,
      context: {'source': 'platform_dispatcher'},
    );
    ErrorHandler.logError(appError);
    return true;
  };

  // Set up method channel for background sync
  const backgroundChannel = MethodChannel('com.quarterlies/background_sync');
  backgroundChannel.setMethodCallHandler((call) async {
    if (call.method == 'performBackgroundSync') {
      try {
        // Initialize sync manager
        final syncManager = SyncManager();
        await syncManager.initialize();

        // Perform sync
        await syncManager.syncData();

        // Notify native code that sync is complete
        await backgroundChannel.invokeMethod('syncComplete');
        return true;
      } catch (e) {
        debugPrint('Error during background sync: $e');
        return false;
      }
    }
    return null;
  });

  // Set up method channel for deep linking
  const deepLinkingChannel = MethodChannel('com.quarterlies/deep_linking');
  deepLinkingChannel.setMethodCallHandler((call) async {
    if (call.method == 'navigate_to_update_password') {
      // Use the global navigator key to navigate from anywhere
      navigatorKey.currentState?.pushNamed('/update-password');
      return true;
    }
    return null;
  });

  await Supabase.initialize(
    url: 'https://hoeagvrddekfmeqqkxca.supabase.co',
    anonKey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhvZWFndnJkZGVrZm1lcXFreGNhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyODMyNTAsImV4cCI6MjA2MTg1OTI1MH0.gU_8hNKC_HwEc-7fCJC6ARyZbDO92lCet6RBfuvx4Yo',
  );

  // Initialize services with error handling for web platform
  try {
    // Initialize notification service (may not work on web)
    final notificationService = NotificationService();
    await notificationService.initialize();

    // Schedule daily check for due invoices (may not work on web)
    await notificationService.scheduleDailyDueInvoiceCheck();
  } catch (e) {
    debugPrint(
      'Notification service initialization failed (expected on web): $e',
    );
  }

  try {
    // Perform initial check for due invoices (may not work on web)
    final invoiceNotificationChecker = InvoiceNotificationChecker();
    await invoiceNotificationChecker.checkForDueInvoices();
  } catch (e) {
    debugPrint('Invoice notification check failed (expected on web): $e');
  }

  try {
    // Initialize mileage tracking service (may not work on web)
    final mileageTrackingService = MileageTrackingService();
    await mileageTrackingService.initialize();
  } catch (e) {
    debugPrint(
      'Mileage tracking service initialization failed (expected on web): $e',
    );
  }

  try {
    // Initialize offline data repository and sync services (may not work on web)
    // Note: LoadingStateProvider will be injected later via providers
    final dataRepository = DataRepository();
    await dataRepository.initialize();
  } catch (e) {
    debugPrint('Data repository initialization failed (expected on web): $e');
  }

  try {
    // Initialize sync manager for background sync (may not work on web)
    final syncManager = SyncManager();
    await syncManager.initialize();
  } catch (e) {
    debugPrint('Sync manager initialization failed (expected on web): $e');
  }

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SecurityProvider()),
        ChangeNotifierProvider(create: (_) => DisplaySettingsProvider()),
        ChangeNotifierProvider(create: (_) => LoadingStateProvider()),
        ChangeNotifierProxyProvider<LoadingStateProvider, CustomerProvider>(
          create:
              (context) => CustomerProvider(
                loadingStateProvider: Provider.of<LoadingStateProvider>(
                  context,
                  listen: false,
                ),
              ),
          update:
              (context, loadingStateProvider, previous) =>
                  CustomerProvider(loadingStateProvider: loadingStateProvider),
        ),
        ChangeNotifierProvider(create: (_) => JobProvider()),
        ChangeNotifierProvider(create: (_) => FinancialProvider()),
        ChangeNotifierProvider(create: (_) => TaxPaymentProvider()),
        ChangeNotifierProvider(create: (_) => InvoiceProvider()),
        ChangeNotifierProvider(create: (_) => ExpenseProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  bool _isCheckingAutoLogin = true;
  bool _shouldAutoLogin = false;

  @override
  void initState() {
    super.initState();
    // Initialize all providers and check auto-login
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeProviders();
      _checkAutoLogin();
    });
  }

  /// Initialize all providers
  Future<void> _initializeProviders() async {
    try {
      // Get LoadingStateProvider before async operations
      final loadingStateProvider = Provider.of<LoadingStateProvider>(
        context,
        listen: false,
      );

      // Initialize providers in parallel for better performance
      await Future.wait([
        Provider.of<DisplaySettingsProvider>(
          context,
          listen: false,
        ).initialize(),
        Provider.of<CustomerProvider>(context, listen: false).initialize(),
        Provider.of<JobProvider>(context, listen: false).initialize(),
        Provider.of<FinancialProvider>(context, listen: false).initialize(),
        Provider.of<TaxPaymentProvider>(context, listen: false).initialize(),
        Provider.of<InvoiceProvider>(context, listen: false).initialize(),
        Provider.of<ExpenseProvider>(context, listen: false).initialize(),
      ]);

      // Set up SyncManager with LoadingStateProvider
      final syncManager = SyncManager();
      syncManager.setLoadingStateProvider(loadingStateProvider);

      // Set up VoiceRecordingService with LoadingStateProvider
      final voiceRecordingService = VoiceRecordingService();
      voiceRecordingService.setLoadingStateProvider(loadingStateProvider);

      // Set up PDF services with LoadingStateProvider
      final invoicePdfService = InvoicePdfService();
      invoicePdfService.setLoadingStateProvider(loadingStateProvider);

      final contractPdfService = ContractPdfService();
      contractPdfService.setLoadingStateProvider(loadingStateProvider);

      final estimatePdfService = EstimatePdfService();
      estimatePdfService.setLoadingStateProvider(loadingStateProvider);

      // Set up OCR service with LoadingStateProvider
      // Note: OcrService is initialized when needed in ExpenseFormScreen

      // Set up ReportService with LoadingStateProvider
      // Note: ReportService is initialized when needed in ReportsScreen
    } catch (e) {
      debugPrint('Provider initialization failed: $e');
    }
  }

  /// Check if auto-login should be attempted
  Future<void> _checkAutoLogin() async {
    try {
      final securityProvider = Provider.of<SecurityProvider>(
        context,
        listen: false,
      );
      final authService = securityProvider.authService;

      // If user is already logged in, no need to check auto-login
      if (authService.isLoggedIn) {
        setState(() {
          _isCheckingAutoLogin = false;
          _shouldAutoLogin = false;
        });
        return;
      }

      // Attempt auto-login
      final autoLoginSuccess = await authService.attemptAutoLogin();

      if (mounted) {
        setState(() {
          _isCheckingAutoLogin = false;
          _shouldAutoLogin = autoLoginSuccess;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isCheckingAutoLogin = false;
          _shouldAutoLogin = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Access security services through the provider
    final securityProvider = Provider.of<SecurityProvider>(
      context,
      listen: false,
    );
    final authService = securityProvider.authService;

    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        // Show loading screen while checking auto-login
        if (_isCheckingAutoLogin) {
          return MaterialApp(
            title: 'Quarterlies',
            theme: FieldFriendlyTheme.getThemeForDisplayMode(
              displayProvider.displayMode,
              isDark: false,
            ),
            home: const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        // Determine initial route based on auth state and auto-login result
        String initialRoute;
        if (authService.isLoggedIn || _shouldAutoLogin) {
          initialRoute = '/auth-wrapper';
        } else {
          initialRoute = '/login';
        }

        return MaterialApp(
          navigatorKey: navigatorKey,
          onGenerateRoute: (settings) {
            // Handle deep links
            if (settings.name != null &&
                settings.name!.startsWith('/reset-password')) {
              return MaterialPageRoute(
                builder: (context) => const UpdatePasswordScreen(),
              );
            }
            return null;
          },
          title: 'Quarterlies',
          theme: FieldFriendlyTheme.getThemeForDisplayMode(
            displayProvider.displayMode,
            isDark: false,
          ),
          darkTheme: FieldFriendlyTheme.getThemeForDisplayMode(
            displayProvider.displayMode,
            isDark: true,
          ),
          themeMode: ThemeMode.system, // Automatically adapt to system theme
          // Define named routes for navigation
          initialRoute: initialRoute,
          routes: {
            '/': (context) => const MainNavigationScreen(),
            '/home': (context) => const MainNavigationScreen(),
            '/main': (context) => const MainNavigationScreen(),
            '/auth-wrapper': (context) => const AuthWrapperScreen(),
            '/login': (context) => const LoginScreen(),
            '/signup': (context) => const SignupScreen(),
            '/password-reset': (context) => const PasswordResetScreen(),
            '/update-password': (context) => const UpdatePasswordScreen(),
            '/onboarding': (context) => const OnboardingFlowScreen(),
            '/customers': (context) => const CustomerListScreen(),
            '/jobs': (context) => const JobListScreen(),
            '/mileage': (context) => const MileageListScreen(),
          },
        );
      },
    );
  }
}
