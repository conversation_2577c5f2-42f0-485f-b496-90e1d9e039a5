import 'dart:convert';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class EncryptionService {
  late encrypt.Encrypter _encrypter;
  late encrypt.IV _iv;
  final FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  // Initialize encryption with a key stored securely
  Future<void> initialize() async {
    // Get or generate encryption key
    String? storedKey = await _secureStorage.read(key: 'encryption_key');
    if (storedKey == null) {
      final key = encrypt.Key.fromSecureRandom(32);
      await _secureStorage.write(
        key: 'encryption_key',
        value: base64Encode(key.bytes),
      );
      storedKey = base64Encode(key.bytes);
    }

    // Create encrypter with AES
    final key = encrypt.Key.fromBase64(storedKey);
    _iv = encrypt.IV.fromLength(16); // Generate a secure IV
    _encrypter = encrypt.Encrypter(encrypt.AES(key));
  }

  // Encrypt sensitive data
  String encryptData(String plainText) {
    final encrypted = _encrypter.encrypt(plainText, iv: _iv);
    return encrypted.base64;
  }

  // Decrypt data
  String decryptData(String encryptedText) {
    final encrypted = encrypt.Encrypted.fromBase64(encryptedText);
    return _encrypter.decrypt(encrypted, iv: _iv);
  }

  // Store IV securely for each encrypted value
  Future<void> storeIV(String dataKey) async {
    await _secureStorage.write(
      key: 'iv_$dataKey',
      value: base64Encode(_iv.bytes),
    );
  }

  // Retrieve IV for a specific encrypted value
  Future<encrypt.IV?> retrieveIV(String dataKey) async {
    final ivString = await _secureStorage.read(key: 'iv_$dataKey');
    if (ivString == null) return null;

    return encrypt.IV.fromBase64(ivString);
  }

  // Encrypt sensitive financial data with unique IV
  Future<String> encryptFinancialData(String plainText, String dataKey) async {
    // Generate a unique IV for this data
    _iv = encrypt.IV.fromSecureRandom(16);

    // Store the IV with a key related to this data
    await storeIV(dataKey);

    // Encrypt the data
    final encrypted = _encrypter.encrypt(plainText, iv: _iv);
    return encrypted.base64;
  }

  // Decrypt financial data with its unique IV
  Future<String> decryptFinancialData(
    String encryptedText,
    String dataKey,
  ) async {
    // Retrieve the IV used for this data
    final iv = await retrieveIV(dataKey);
    if (iv == null) {
      throw Exception('IV not found for this data');
    }

    // Decrypt using the specific IV
    final encrypted = encrypt.Encrypted.fromBase64(encryptedText);
    return _encrypter.decrypt(encrypted, iv: iv);
  }
}
