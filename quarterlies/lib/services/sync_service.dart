import 'dart:async';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/services/connectivity_service.dart';
import 'package:quarterlies/services/local_database_service.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/retry_mechanism.dart';

class SyncService {
  // Singleton pattern
  static final SyncService _instance = SyncService._internal();
  factory SyncService({LoadingStateProvider? loadingStateProvider}) {
    if (loadingStateProvider != null) {
      _instance._loadingStateProvider = loadingStateProvider;
    }
    return _instance;
  }
  SyncService._internal();

  final ConnectivityService _connectivityService = ConnectivityService();
  final LocalDatabaseService _localDatabaseService = LocalDatabaseService();
  final SupabaseService _supabaseService = SupabaseService();

  LoadingStateProvider? _loadingStateProvider;

  // Stream controller for sync status updates
  final _syncStatusController = StreamController<SyncStatus>.broadcast();
  Stream<SyncStatus> get syncStatus => _syncStatusController.stream;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  // Timer for periodic sync
  Timer? _periodicSyncTimer;

  // Timer for periodic sync attempts
  // Removed unused _syncTimer field
  bool _isSyncing = false;

  // Initialize the service
  void initialize() {
    // Listen for connectivity changes
    _connectivitySubscription = _connectivityService.connectionStatus.listen((
      isConnected,
    ) {
      if (isConnected && !_isSyncing) {
        // When connection is restored, attempt to sync
        syncData();
      }
    });

    // Start periodic sync timer (every 5 minutes)
    _periodicSyncTimer = Timer.periodic(const Duration(minutes: 5), (_) async {
      final isConnected = await _connectivityService.checkConnection();
      if (isConnected && !_isSyncing) {
        syncData();
      }
    });

    // Initialize connectivity service
    _connectivityService.initialize();
  }

  // Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _periodicSyncTimer?.cancel();
    _syncStatusController.close();
  }

  // Main sync function
  Future<void> syncData() async {
    if (_isSyncing) return;

    _isSyncing = true;
    _syncStatusController.add(
      SyncStatus(status: 'syncing', message: 'Syncing data...'),
    );

    if (_loadingStateProvider != null) {
      await _loadingStateProvider!.executeWithSyncLoading(
        () async {
          final result = await RetryMechanism.execute(
            () async {
              // Sync customers
              await _syncCustomers();

              // Sync jobs
              await _syncJobs();

              // Sync invoices
              await _syncInvoices();

              // Sync payments
              await _syncPayments();

              // Sync expenses
              await _syncExpenses();

              // Sync time logs
              await _syncTimeLogs();

              // Sync mileage records
              await _syncMileage();

              return true;
            },
            config: RetryMechanism.networkRetryConfig(),
            operationName: 'syncData',
          );

          if (result.isSuccess) {
            _syncStatusController.add(
              SyncStatus(
                status: 'success',
                message: 'Sync completed successfully',
              ),
            );
          } else {
            final appError = result.error!;
            ErrorHandler.logError(appError);
            _syncStatusController.add(
              SyncStatus(
                status: 'error',
                message: ErrorHandler.getUserFriendlyMessage(appError),
              ),
            );
            throw appError;
          }
        },
        operationName: 'Syncing all data',
        errorMessage: 'Failed to sync data',
      );
    } else {
      // Fallback to original implementation
      final result = await RetryMechanism.execute(
        () async {
          // Sync customers
          await _syncCustomers();

          // Sync jobs
          await _syncJobs();

          // Sync invoices
          await _syncInvoices();

          // Sync payments
          await _syncPayments();

          // Sync expenses
          await _syncExpenses();

          // Sync time logs
          await _syncTimeLogs();

          // Sync mileage records
          await _syncMileage();

          return true;
        },
        config: RetryMechanism.networkRetryConfig(),
        operationName: 'syncData',
      );

      if (result.isSuccess) {
        _syncStatusController.add(
          SyncStatus(status: 'success', message: 'Sync completed successfully'),
        );
      } else {
        final appError = result.error!;
        ErrorHandler.logError(appError);
        _syncStatusController.add(
          SyncStatus(
            status: 'error',
            message: ErrorHandler.getUserFriendlyMessage(appError),
          ),
        );
      }
    }

    _isSyncing = false;
  }

  // Sync customers
  Future<void> _syncCustomers() async {
    // Get pending customers from local database
    final pendingCustomers =
        await _localDatabaseService.getPendingSyncCustomers();

    for (var customerMap in pendingCustomers) {
      try {
        // Remove sync_status before creating Customer object
        customerMap.remove('sync_status');
        final customer = Customer.fromJson(customerMap);

        // Add or update customer in Supabase
        await _supabaseService.addCustomer(customer);

        // Update sync status to 'synced'
        await _localDatabaseService.updateCustomerSyncStatus(
          customer.id,
          'synced',
        );
      } catch (e) {
        // If sync fails, keep as pending
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'syncCustomer',
            'customerId': customerMap['id'],
          },
        );
        ErrorHandler.logError(appError);
      }
    }

    // Get all customers from Supabase to ensure local DB is up-to-date
    try {
      final remoteCustomers = await _supabaseService.getCustomers();

      // Store each remote customer in local database with 'synced' status
      for (var customer in remoteCustomers) {
        // Check for conflicts using Last Write Wins strategy
        final localCustomer = await _localDatabaseService.getCustomerById(
          customer.id,
        );
        if (localCustomer != null) {
          // Only update if remote version is newer or if local is not pending sync
          if (_shouldUseRemoteVersion(
            localCustomer.updatedAt,
            customer.updatedAt,
            localCustomer.syncStatus.name,
          )) {
            await _localDatabaseService.insertCustomer(
              customer,
              syncStatus: 'synced',
            );
          }
        } else {
          // No local version exists, simply insert
          await _localDatabaseService.insertCustomer(
            customer,
            syncStatus: 'synced',
          );
        }
      }
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'fetchRemoteCustomers'},
      );
      ErrorHandler.logError(appError);
    }
  }

  // Sync jobs
  Future<void> _syncJobs() async {
    // Get pending jobs from local database
    final pendingJobs = await _localDatabaseService.getPendingSyncJobs();

    for (var jobMap in pendingJobs) {
      try {
        jobMap.remove('sync_status');
        final job = Job.fromJson(jobMap);

        // Add or update job in Supabase
        await _supabaseService.addJob(job);

        // Update job sync status to 'synced'
        // Use direct database update since updateJobSyncStatus doesn't exist
        final db = await _localDatabaseService.database;
        await db.update(
          'jobs',
          {'sync_status': 'synced'},
          where: 'id = ?',
          whereArgs: [job.id],
        );
      } catch (e) {
        final appError = AppError.fromException(
          e,
          context: {'operation': 'syncJob', 'jobId': jobMap['id']},
        );
        ErrorHandler.logError(appError);
      }
    }

    // Get all jobs from Supabase
    try {
      final remoteJobs = await _supabaseService.getJobs();

      // Store each remote job in local database using Last Write Wins strategy
      for (var job in remoteJobs) {
        final localJob = await _localDatabaseService.getJobById(job.id);
        if (localJob != null) {
          // Only update if remote version is newer or if local is not pending sync
          if (_shouldUseRemoteVersion(
            localJob.updatedAt,
            job.updatedAt,
            localJob.syncStatus.name,
          )) {
            await _localDatabaseService.insertJob(job, syncStatus: 'synced');
          }
        } else {
          // No local version exists, simply insert
          await _localDatabaseService.insertJob(job, syncStatus: 'synced');
        }
      }
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'fetchRemoteJobs'},
      );
      ErrorHandler.logError(appError);
    }
  }

  // Sync invoices
  Future<void> _syncInvoices() async {
    // Get pending invoices from local database
    final pendingInvoices =
        await _localDatabaseService.getPendingSyncInvoices();

    for (var invoiceMap in pendingInvoices) {
      try {
        invoiceMap.remove('sync_status');

        // Get line items for this invoice
        final db = await _localDatabaseService.database;
        final List<Map<String, dynamic>> itemMaps = await db.query(
          'invoice_items',
          where: 'invoice_id = ?',
          whereArgs: [invoiceMap['id']],
        );

        // Remove sync_status from item maps
        for (var itemMap in itemMaps) {
          itemMap.remove('sync_status');
        }

        // Add line items to invoice map
        invoiceMap['line_items'] = itemMaps;

        final invoice = Invoice.fromJson(invoiceMap);

        // Add or update invoice in Supabase
        await _supabaseService.addInvoice(invoice);

        // Update sync status to 'synced'
        if (invoice.id != null) {
          await _localDatabaseService.updateInvoiceSyncStatus(
            invoice.id!,
            'synced',
          );
        }
      } catch (e) {
        final appError = AppError.fromException(
          e,
          context: {'operation': 'syncInvoice', 'invoiceId': invoiceMap['id']},
        );
        ErrorHandler.logError(appError);
      }
    }

    // Get all invoices from Supabase
    try {
      final remoteInvoices = await _supabaseService.getInvoices();

      // Store each remote invoice in local database
      for (var invoice in remoteInvoices) {
        await _localDatabaseService.insertInvoice(
          invoice,
          syncStatus: 'synced',
        );
      }
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'fetchRemoteInvoices'},
      );
      ErrorHandler.logError(appError);
    }
  }

  // Sync payments
  Future<void> _syncPayments() async {
    // Get pending payments from local database
    final pendingPayments =
        await _localDatabaseService.getPendingSyncPayments();

    for (var paymentMap in pendingPayments) {
      try {
        paymentMap.remove('sync_status');
        final payment = Payment.fromJson(paymentMap);

        // Add or update payment in Supabase
        await _supabaseService.addPayment(payment);

        // Update sync status to 'synced'
        await _localDatabaseService.updatePaymentSyncStatus(
          payment.id,
          'synced',
        );
      } catch (e) {
        final appError = AppError.fromException(
          e,
          context: {'operation': 'syncPayment', 'paymentId': paymentMap['id']},
        );
        ErrorHandler.logError(appError);
      }
    }

    // Get all payments from Supabase
    try {
      final remotePayments = await _supabaseService.getPayments();

      // Store each remote payment in local database
      for (var payment in remotePayments) {
        await _localDatabaseService.insertPayment(
          payment,
          syncStatus: 'synced',
        );
      }
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'fetchRemotePayments'},
      );
      ErrorHandler.logError(appError);
    }
  }

  // Sync expenses
  Future<void> _syncExpenses() async {
    // Get pending expenses from local database
    final pendingExpenses =
        await _localDatabaseService.getPendingSyncExpenses();

    for (var expenseMap in pendingExpenses) {
      try {
        expenseMap.remove('sync_status');

        // Convert tags string back to list if needed
        if (expenseMap['tags'] != null && expenseMap['tags'] is String) {
          expenseMap['tags'] = expenseMap['tags'].split(',');
        }

        final expense = Expense.fromJson(expenseMap);

        // Add or update expense in Supabase
        await _supabaseService.addExpense(expense);

        // Update sync status to 'synced'
        await _localDatabaseService.updateExpenseSyncStatus(
          expense.id,
          'synced',
        );
      } catch (e) {
        final appError = AppError.fromException(
          e,
          context: {'operation': 'syncExpense', 'expenseId': expenseMap['id']},
        );
        ErrorHandler.logError(appError);
      }
    }

    // Get all expenses from Supabase
    try {
      final remoteExpenses = await _supabaseService.getExpenses();

      // Store each remote expense in local database using Last Write Wins strategy
      for (var expense in remoteExpenses) {
        // Use the existing method from LocalDatabaseService to get expense by ID
        final db = await _localDatabaseService.database;
        final List<Map<String, dynamic>> maps = await db.query(
          'expenses',
          where: 'id = ?',
          whereArgs: [expense.id],
          limit: 1,
        );

        if (maps.isNotEmpty) {
          final localExpenseMap = maps.first;
          // Convert tags string back to list if needed
          if (localExpenseMap['tags'] != null &&
              localExpenseMap['tags'] is String) {
            localExpenseMap['tags'] = localExpenseMap['tags'].split(',');
          }

          // Only update if remote version is newer or if local is not pending sync
          if (_shouldUseRemoteVersion(
            localExpenseMap['updated_at'],
            expense.updatedAt,
            localExpenseMap['sync_status'],
          )) {
            await _localDatabaseService.insertExpense(
              expense,
              syncStatus: 'synced',
            );
          }
        } else {
          // No local version exists, simply insert
          await _localDatabaseService.insertExpense(
            expense,
            syncStatus: 'synced',
          );
        }
      }
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'fetchRemoteExpenses'},
      );
      ErrorHandler.logError(appError);
    }
  }

  // Sync time logs
  Future<void> _syncTimeLogs() async {
    // Get pending time logs from local database
    final pendingTimeLogs =
        await _localDatabaseService.getPendingSyncTimeLogs();

    for (var timeLogMap in pendingTimeLogs) {
      try {
        timeLogMap.remove('sync_status');
        final timeLog = TimeLog.fromJson(timeLogMap);

        // Add or update time log in Supabase
        await _supabaseService.addTimeLog(timeLog);

        // Update sync status to 'synced'
        await _localDatabaseService.updateTimeLogSyncStatus(
          timeLog.id,
          'synced',
        );
      } catch (e) {
        final appError = AppError.fromException(
          e,
          context: {'operation': 'syncTimeLog', 'timeLogId': timeLogMap['id']},
        );
        ErrorHandler.logError(appError);
      }
    }

    // Get all time logs from Supabase
    try {
      final remoteTimeLogs = await _supabaseService.getTimeLogs();

      // Store each remote time log in local database using Last Write Wins strategy
      for (var timeLog in remoteTimeLogs) {
        final localTimeLog = await _localDatabaseService.getTimeLogById(
          timeLog.id,
        );
        if (localTimeLog != null) {
          // Only update if remote version is newer or if local is not pending sync
          if (_shouldUseRemoteVersion(
            localTimeLog.updatedAt,
            timeLog.updatedAt,
            localTimeLog.syncStatus.name,
          )) {
            await _localDatabaseService.insertTimeLog(
              timeLog,
              syncStatus: 'synced',
            );
          }
        } else {
          // No local version exists, simply insert
          await _localDatabaseService.insertTimeLog(
            timeLog,
            syncStatus: 'synced',
          );
        }
      }
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'fetchRemoteTimeLogs'},
      );
      ErrorHandler.logError(appError);
    }
  }

  // Force a manual sync
  Future<void> forceSyncData() async {
    final isConnected = await _connectivityService.checkConnection();
    if (isConnected) {
      return syncData();
    } else {
      _syncStatusController.add(
        SyncStatus(
          status: 'error',
          message: 'No internet connection available',
        ),
      );
    }
  }

  // Sync mileage records
  Future<void> _syncMileage() async {
    // Get pending mileage records from local database
    // Use direct database query instead of getPendingSyncMileage
    final db = await _localDatabaseService.database;
    final List<Map<String, dynamic>> pendingMileage = await db.query(
      'expenses',
      where: 'sync_status = ? AND type = ?',
      whereArgs: ['pending', 'Mileage'],
    );

    for (var mileageMap in pendingMileage) {
      try {
        // Remove sync_status before creating Mileage object
        mileageMap.remove('sync_status');

        // Convert tags string back to list if needed
        if (mileageMap['tags'] != null && mileageMap['tags'] is String) {
          mileageMap['tags'] = mileageMap['tags'].split(',');
        }

        final mileage = Mileage.fromJson(mileageMap);

        // Add or update mileage in Supabase
        await _supabaseService.addMileage(mileage);

        // Update sync status to 'synced'
        // Use updateExpenseSyncStatus instead of updateMileageSyncStatus
        await _localDatabaseService.updateExpenseSyncStatus(
          mileage.id,
          'synced',
        );
      } catch (e) {
        final appError = AppError.fromException(
          e,
          context: {'operation': 'syncMileage', 'mileageId': mileageMap['id']},
        );
        ErrorHandler.logError(appError);
      }
    }

    // Get all mileage records from Supabase
    try {
      // Use getMileageEntries instead of getMileage
      final remoteMileage = await _supabaseService.getMileageEntries();

      // Store each remote mileage in local database using Last Write Wins strategy
      for (var mileage in remoteMileage) {
        // Use direct database query instead of getMileageById
        final db = await _localDatabaseService.database;
        final List<Map<String, dynamic>> maps = await db.query(
          'expenses',
          where: 'id = ? AND type = ?',
          whereArgs: [mileage.id, 'Mileage'],
          limit: 1,
        );

        if (maps.isNotEmpty) {
          final localMileageMap = maps.first;
          // Convert tags string back to list if needed
          if (localMileageMap['tags'] != null &&
              localMileageMap['tags'] is String) {
            localMileageMap['tags'] = localMileageMap['tags'].split(',');
          }

          // Only update if remote version is newer or if local is not pending sync
          if (_shouldUseRemoteVersion(
            localMileageMap['updated_at'],
            mileage.updatedAt,
            localMileageMap['sync_status'],
          )) {
            // Use insertExpense instead of insertMileage since Mileage extends Expense
            await _localDatabaseService.insertExpense(
              mileage,
              syncStatus: 'synced',
            );
          }
        } else {
          // No local version exists, simply insert
          // Use insertExpense instead of insertMileage since Mileage extends Expense
          await _localDatabaseService.insertExpense(
            mileage,
            syncStatus: 'synced',
          );
        }
      }
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'fetchRemoteMileage'},
      );
      ErrorHandler.logError(appError);
    }
  }

  // Helper method to determine if remote version should be used with improved conflict detection
  bool _shouldUseRemoteVersion(
    dynamic localUpdatedAt,
    DateTime? remoteUpdatedAt,
    String syncStatus,
  ) {
    // If local version is pending sync, don't overwrite with remote
    if (syncStatus == 'pending') return false;

    // If local version has a conflict that hasn't been resolved, don't overwrite
    if (syncStatus == 'conflict') return false;

    // If no remote updated timestamp, use local
    if (remoteUpdatedAt == null) return false;

    // Handle different types of localUpdatedAt
    if (localUpdatedAt == null) {
      // If no local updated timestamp, use remote
      return true;
    } else if (localUpdatedAt is DateTime) {
      // If localUpdatedAt is already a DateTime, compare directly
      // Check for potential conflict (changes made within 5 minutes of each other)
      final difference = remoteUpdatedAt.difference(localUpdatedAt).abs();
      if (difference.inMinutes < 5 && syncStatus == 'synced') {
        // Mark as conflict if changes were made close to each other
        // This will be handled by the conflict resolution UI
        _markAsConflict(localUpdatedAt, remoteUpdatedAt);
        return false;
      }
      return remoteUpdatedAt.isAfter(localUpdatedAt);
    } else if (localUpdatedAt is String) {
      // If localUpdatedAt is a String, parse it first
      try {
        final parsedLocalUpdatedAt = DateTime.parse(localUpdatedAt);
        // Check for potential conflict (changes made within 5 minutes of each other)
        final difference =
            remoteUpdatedAt.difference(parsedLocalUpdatedAt).abs();
        if (difference.inMinutes < 5 && syncStatus == 'synced') {
          // Mark as conflict if changes were made close to each other
          _markAsConflict(parsedLocalUpdatedAt, remoteUpdatedAt);
          return false;
        }
        return remoteUpdatedAt.isAfter(parsedLocalUpdatedAt);
      } catch (e) {
        // If parsing fails, default to using remote version
        return true;
      }
    }

    // Default to using remote version for any other case
    return true;
  }

  // Helper method to mark an item as having a conflict
  void _markAsConflict(DateTime localTime, DateTime remoteTime) {
    // Log the conflict for debugging
    final appError = AppError(
      type: ErrorType.database,
      severity: ErrorSeverity.medium,
      message: 'Sync conflict detected',
      userFriendlyMessage:
          'Data conflict detected during sync. Please resolve manually.',
      context: {
        'operation': 'syncConflictDetection',
        'localTime': localTime.toIso8601String(),
        'remoteTime': remoteTime.toIso8601String(),
      },
    );
    ErrorHandler.logError(appError);

    // We'll update the sync status to 'conflict' in the specific sync methods
    // This is just a placeholder for now - in a real implementation, we would
    // store both versions and let the user decide which one to keep

    // Notify the user about the conflict
    _syncStatusController.add(
      SyncStatus(
        status: 'conflict',
        message: 'Sync conflict detected. Please resolve manually.',
      ),
    );
  }
}

// Class to represent sync status updates
class SyncStatus {
  final String status; // 'syncing', 'success', 'error'
  final String message;

  SyncStatus({required this.status, required this.message});

  static final SyncStatus pending = SyncStatus(
    status: 'pending',
    message: 'Pending sync',
  );
  static final SyncStatus syncing = SyncStatus(
    status: 'syncing',
    message: 'Syncing data...',
  );
  static final SyncStatus success = SyncStatus(
    status: 'success',
    message: 'Sync completed successfully',
  );
  static final SyncStatus error = SyncStatus(
    status: 'error',
    message: 'Sync failed',
  );
}
