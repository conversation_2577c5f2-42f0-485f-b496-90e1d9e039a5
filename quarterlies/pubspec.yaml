name: quarterlies
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.7.2 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  supabase_flutter: ^2.9.0 # Updated to latest stable version
  uuid: ^4.5.1 # Updated to latest version for generating unique IDs
  flutter_secure_storage: ^9.2.4 # Maintained for stability
  encrypt: ^5.0.3 # Updated for client-side encryption
  html_unescape: ^2.0.0 # For HTML entity decoding in sanitization
  dio: ^5.8.0 # Updated HTTP client for network requests
  provider: ^6.1.5 # Updated for state management and dependency injection
  crypto: ^3.0.6 # Updated for cryptographic functions including certificate fingerprinting
  image_picker: ^1.1.2 # Updated for selecting images from gallery or camera
  intl: ^0.18.1 # Maintained for compatibility
  flutter_local_notifications: ^19.2.1 # Updated to latest version
  fl_chart: ^1.0.0 # Maintained stable version
  # Note: Certificate pinning will be implemented using Dio interceptors instead of dio_pinning

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  google_mlkit_text_recognition: ^0.15.0
  pdf: ^3.11.3
  path_provider: ^2.1.5
  share_plus: ^11.0.0
  flutter_pdfview: ^1.4.0+1
  sqflite: ^2.4.2
  connectivity_plus: ^6.1.4
  path: ^1.9.1
  sqflite_common_ffi: ^2.3.5
  workmanager: ^0.5.2
  background_fetch: ^1.2.1
  http: ^1.1.0
  url_launcher: ^6.1.14
  signature: ^5.4.0
  device_info_plus: ^9.1.2 # Updated to latest version
  package_info_plus: ^8.3.0 # Updated to latest version
  printing: ^5.14.2 # Updated to latest version
  shared_preferences: ^2.5.3 # Updated to latest version

  geolocator: ^14.0.1 # Updated to latest version
  geocoding: ^3.0.0 # Maintained stable version
  flutter_sound: ^9.28.0 # Maintained for compatibility
  permission_handler: ^12.0.0 # Maintained stable version
  speech_to_text: ^7.0.0 # Maintained stable version
  csv: ^6.0.0 # Maintained stable version
dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.6 # Updated to latest version
  build_runner: ^2.4.15 # Updated to latest version
  mocktail: ^1.0.4 # Alternative to mockito for easier mocking
  fake_async: ^1.3.1 # For testing async code with fake timers

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0 # Maintained for compatibility

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
